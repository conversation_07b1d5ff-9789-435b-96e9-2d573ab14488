import { app, BrowserWindow, Menu, ipcMain, shell, dialog } from 'electron'
import { join } from 'path'
import { isDev } from './utils'
import { setupIPC } from './ipc'
import { createMenu } from './menu'

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口将自动关闭
let mainWindow: BrowserWindow | null = null

// 创建主窗口
function createMainWindow(): void {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false, // 先不显示，等待ready-to-show事件
    icon: join(__dirname, '../../public/icon.png'), // 应用图标
    webPreferences: {
      nodeIntegration: false, // 出于安全考虑禁用node集成
      contextIsolation: true, // 启用上下文隔离
      enableRemoteModule: false, // 禁用remote模块
      preload: join(__dirname, 'preload.js'), // 预加载脚本
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
  })

  // 加载应用
  if (isDev) {
    // 开发环境加载本地服务器
    mainWindow.loadURL('http://localhost:5173')
    // 打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产环境加载打包后的文件
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // 当窗口准备好显示时显示窗口
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show()
      
      // 如果是开发环境，聚焦窗口
      if (isDev) {
        mainWindow.focus()
      }
    }
  })

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 处理窗口创建请求（安全性）
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })

  // 阻止导航到外部URL（安全性）
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.origin !== 'file://') {
      event.preventDefault()
    }
  })
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  // 创建主窗口
  createMainWindow()
  
  // 设置应用菜单
  const menu = createMenu(mainWindow)
  Menu.setApplicationMenu(menu)
  
  // 设置IPC通信
  setupIPC(mainWindow)
  
  // 在macOS上，当点击dock图标且没有其他窗口打开时，重新创建窗口
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow()
    }
  })
})

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用通常保持活动状态，即使没有窗口，直到用户明确退出
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 在此文件中，你可以包含应用的其他主进程代码
// 你也可以将它们放在单独的文件中并在此处引入

// 安全性：防止新窗口创建
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    // 阻止新窗口创建，改为在默认浏览器中打开
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})

// 处理证书错误（开发环境）
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev) {
    // 在开发环境中忽略证书错误
    event.preventDefault()
    callback(true)
  } else {
    // 在生产环境中使用默认行为
    callback(false)
  }
})

// 导出主窗口引用供其他模块使用
export { mainWindow }
