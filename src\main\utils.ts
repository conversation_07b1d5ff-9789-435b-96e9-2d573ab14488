import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'

// 判断是否为开发环境
export const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

// 获取应用数据目录
export function getAppDataPath(): string {
  const userDataPath = app.getPath('userData')
  const appDataDir = join(userDataPath, 'TaskCalendar')
  
  // 确保目录存在
  if (!existsSync(appDataDir)) {
    mkdirSync(appDataDir, { recursive: true })
  }
  
  return appDataDir
}

// 获取任务数据文件路径
export function getTasksFilePath(): string {
  return join(getAppDataPath(), 'tasks.json')
}

// 获取设置文件路径
export function getSettingsFilePath(): string {
  return join(getAppDataPath(), 'settings.json')
}

// 获取备份目录路径
export function getBackupDirPath(): string {
  const backupDir = join(getAppDataPath(), 'backups')
  
  // 确保备份目录存在
  if (!existsSync(backupDir)) {
    mkdirSync(backupDir, { recursive: true })
  }
  
  return backupDir
}

// 格式化日期为文件名安全的字符串
export function formatDateForFilename(date: Date): string {
  return date.toISOString().split('T')[0]
}

// 日志函数
export function log(message: string, ...args: any[]): void {
  if (isDev) {
    console.log(`[Main Process] ${message}`, ...args)
  }
}

// 错误日志函数
export function logError(message: string, error?: any): void {
  console.error(`[Main Process Error] ${message}`, error)
}

// 获取应用版本
export function getAppVersion(): string {
  return app.getVersion()
}

// 获取应用名称
export function getAppName(): string {
  return app.getName()
}
