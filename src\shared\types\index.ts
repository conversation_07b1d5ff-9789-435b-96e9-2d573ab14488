// 任务数据结构
export interface Task {
  id: string;                    // 唯一标识
  title: string;                 // 任务标题
  description?: string;          // 任务描述
  startTime: string;            // 开始时间 (ISO 8601)
  endTime: string;              // 结束时间 (ISO 8601)
  date: string;                 // 任务日期 (YYYY-MM-DD)
  completed: boolean;           // 完成状态
  priority: 'high' | 'medium' | 'low'; // 优先级
  reminder?: {
    enabled: boolean;           // 是否启用提醒
    minutesBefore: number;      // 提前提醒分钟数
  };
  repeat?: {
    type: 'daily' | 'weekly' | 'monthly'; // 重复类型
    interval: number;           // 重复间隔
    endDate?: string;           // 重复结束日期
  };
  createdAt: string;            // 创建时间
  updatedAt: string;            // 更新时间
}

// 应用设置结构
export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  fontSize: 'small' | 'medium' | 'large';
  timeFormat: '12h' | '24h';
  weekStartsOn: 0 | 1; // 0=周日, 1=周一
  autoStartup: boolean;
  reminderSound: boolean;
  windowBounds: {
    width: number;
    height: number;
    x?: number;
    y?: number;
  };
}

// IPC 通信消息类型
export interface IPCMessage {
  type: string;
  payload?: any;
}

// 日历相关类型
export interface CalendarDate {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  tasks: Task[];
}
