{"name": "task-calendar", "version": "1.0.0", "description": "A lightweight personal task management desktop application", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:all": "npm run build && electron-builder", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "keywords": ["electron", "react", "typescript", "task-management", "calendar", "desktop-app"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-electron": "^0.15.5", "wait-on": "^7.2.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7", "date-fns": "^3.0.6", "electron-store": "^8.1.0"}}