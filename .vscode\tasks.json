{"version": "2.0.0", "tasks": [{"type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "label": "npm: dev", "detail": "Start development server"}, {"type": "npm", "script": "build:main", "group": "build", "label": "npm: build:main", "detail": "Build main process"}, {"type": "npm", "script": "build:renderer", "group": "build", "label": "npm: build:renderer", "detail": "Build renderer process"}, {"type": "npm", "script": "build", "group": "build", "label": "npm: build", "detail": "Build entire application"}, {"type": "npm", "script": "lint", "group": "test", "label": "npm: lint", "detail": "Run ESLint"}, {"type": "npm", "script": "lint:fix", "group": "test", "label": "npm: lint:fix", "detail": "Fix ESLint errors"}, {"type": "shell", "command": "npx prettier --write .", "group": "test", "label": "Format with <PERSON><PERSON><PERSON>", "detail": "Format all files with <PERSON><PERSON><PERSON>"}]}