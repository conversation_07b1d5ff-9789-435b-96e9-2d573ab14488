import React, { useState, useMemo } from 'react'
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns'
import { Task } from '@/shared/types'
import '../styles/Calendar.css'

interface CalendarProps {
  selectedDate: Date
  onDateSelect: (date: Date) => void
  tasks: Task[]
}

const Calendar: React.FC<CalendarProps> = ({ selectedDate, onDateSelect, tasks }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date())

  // 生成日历网格数据
  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentMonth)
    const monthEnd = endOfMonth(monthStart)
    const startDate = startOfWeek(monthStart, { weekStartsOn: 1 }) // 周一开始
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 })

    const days = []
    let day = startDate

    while (day <= endDate) {
      days.push({
        date: new Date(day),
        isCurrentMonth: isSameMonth(day, monthStart),
        isToday: isSameDay(day, new Date()),
        isSelected: isSameDay(day, selectedDate),
        tasks: getTasksForDate(day),
      })
      day = addDays(day, 1)
    }

    return days
  }, [currentMonth, selectedDate, tasks])

  // 获取指定日期的任务
  const getTasksForDate = (date: Date): Task[] => {
    const dateStr = format(date, 'yyyy-MM-dd')
    return tasks.filter(task => task.date === dateStr)
  }

  // 处理月份导航
  const handlePrevMonth = () => {
    setCurrentMonth(prev => subMonths(prev, 1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(prev => addMonths(prev, 1))
  }

  // 处理日期点击
  const handleDateClick = (date: Date) => {
    onDateSelect(date)
  }

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      // ESC键处理逻辑
      event.preventDefault()
    }
  }

  return (
    <div className="calendar" onKeyDown={handleKeyDown} tabIndex={0}>
      {/* 月份标题和导航 */}
      <div className="calendar-header">
        <button 
          className="nav-button"
          onClick={handlePrevMonth}
          aria-label="Previous month"
        >
          &#8249;
        </button>
        
        <h2 className="month-title">
          {format(currentMonth, 'MMMM yyyy')}
        </h2>
        
        <button 
          className="nav-button"
          onClick={handleNextMonth}
          aria-label="Next month"
        >
          &#8250;
        </button>
      </div>

      {/* 星期标题 */}
      <div className="calendar-weekdays">
        {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
          <div key={index} className="weekday">
            {day}
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <div className="calendar-grid">
        {calendarDays.map((day, index) => (
          <div
            key={index}
            className={`calendar-day ${
              !day.isCurrentMonth ? 'other-month' : ''
            } ${
              day.isToday ? 'today' : ''
            } ${
              day.isSelected ? 'selected' : ''
            } ${
              day.tasks.length > 0 ? 'has-tasks' : ''
            }`}
            onClick={() => handleDateClick(day.date)}
          >
            <span className="day-number">
              {format(day.date, 'd')}
            </span>
            {day.tasks.length > 0 && (
              <div className="task-indicator">
                <span className="task-count">{day.tasks.length}</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Calendar
