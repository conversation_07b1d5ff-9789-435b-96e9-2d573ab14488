import React, { useState } from 'react'
import Calendar from './components/Calendar'
import TaskList from './components/TaskList'
import { Task } from '@/shared/types'
import './styles/App.css'

const App: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [tasks, setTasks] = useState<Task[]>([])

  // 获取选中日期的任务
  const getTasksForDate = (date: Date): Task[] => {
    const dateStr = date.toISOString().split('T')[0]
    return tasks.filter(task => task.date === dateStr)
  }

  // 处理日期选择
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
  }

  // 处理任务操作
  const handleTaskAdd = (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTask: Task = {
      ...task,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    setTasks(prev => [...prev, newTask])
  }

  const handleTaskUpdate = (taskId: string, updates: Partial<Task>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, ...updates, updatedAt: new Date().toISOString() }
        : task
    ))
  }

  const handleTaskDelete = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
  }

  return (
    <div className="app">
      <div className="app-header">
        <h1>Task Calendar</h1>
      </div>
      
      <div className="app-content">
        <div className="calendar-section">
          <Calendar
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
            tasks={tasks}
          />
        </div>
        
        <div className="tasks-section">
          <TaskList
            date={selectedDate}
            tasks={getTasksForDate(selectedDate)}
            onTaskAdd={handleTaskAdd}
            onTaskUpdate={handleTaskUpdate}
            onTaskDelete={handleTaskDelete}
          />
        </div>
      </div>
    </div>
  )
}

export default App
