.task-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.task-form {
  background-color: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 表单头部 */
.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.form-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-lg);
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  font-size: var(--font-size-xl);
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

/* 表单主体 */
.form-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  font-size: var(--font-size-sm);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-group label {
  margin: 0;
  cursor: pointer;
  user-select: none;
}

.reminder-options {
  margin-left: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.reminder-options label {
  margin: 0;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
}

.reminder-options select {
  width: auto;
  min-width: 150px;
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
}

/* 表单底部 */
.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
}

.cancel-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

.submit-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  background-color: var(--primary-color);
  color: white;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}

.submit-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.submit-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-form-overlay {
    padding: var(--spacing-md);
  }
  
  .task-form {
    max-height: 95vh;
  }
  
  .form-header,
  .form-body,
  .form-footer {
    padding: var(--spacing-md);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .form-footer {
    flex-direction: column-reverse;
  }
  
  .cancel-button,
  .submit-button {
    width: 100%;
    justify-content: center;
  }
  
  .reminder-options {
    flex-direction: column;
    align-items: flex-start;
    margin-left: 0;
    gap: var(--spacing-xs);
  }
  
  .reminder-options select {
    width: 100%;
    min-width: auto;
  }
}
