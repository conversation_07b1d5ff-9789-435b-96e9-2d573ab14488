# Task Calendar

A lightweight personal task management desktop application built with Electron and React.

## Features

- 📅 Clean monthly calendar view
- ✅ Time-based task management
- 🔔 System notifications
- 💾 Local JSON file storage
- 🎨 Light/Dark theme support
- ⌨️ Keyboard shortcuts (ESC)

## Development

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
npm install
```

### Development

```bash
npm run dev
```

### Build

```bash
npm run build
npm run build:all
```

## Project Structure

```
src/
├── main/              # Electron main process
├── renderer/          # React renderer process
│   ├── components/    # React components
│   ├── hooks/         # Custom hooks
│   ├── store/         # State management
│   ├── types/         # TypeScript types
│   ├── utils/         # Utility functions
│   └── styles/        # CSS styles
└── shared/            # Shared code between processes
```

## License

MIT
