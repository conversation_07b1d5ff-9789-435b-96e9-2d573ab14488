{"version":3,"names":["_is","require","_isValidIdentifier","_helperValidatorIdentifier","_helperStringParser","_index","_utils","defineType","defineAliasedType","fields","elements","validate","arrayOf","assertNodeOrValueType","default","process","env","BABEL_TYPES_8_BREAKING","undefined","visitor","aliases","operator","assertValueType","Object","assign","identifier","assertOneOf","ASSIGNMENT_OPERATORS","pattern","node","key","val","validator","is","left","oneOf","assertNodeType","right","builder","BINARY_OPERATORS","expression","inOp","oneOfNodeTypes","value","directives","arrayOfType","body","validateArrayOfType","label","optional","callee","arguments","typeArguments","typeParameters","param","test","consequent","alternate","program","comments","each","assertEach","tokens","type","init","update","functionCommon","params","generator","async","exports","functionTypeAnnotationCommon","returnType","functionDeclarationCommon","declare","id","predicate","parent","inherits","patternLikeCommon","typeAnnotation","decorators","name","chain","isValidIdentifier","TypeError","match","exec","toString","parentKey","nonComp","computed","imported","meta","isKeyword","isReservedWord","deprecatedAlias","Number","isFinite","error","Error","flags","invalid","LOGICAL_OPERATORS","object","property","normal","sourceType","interpreter","properties","kind","shorthand","argument","listKey","index","length","expressions","discriminant","cases","block","handler","finalizer","prefix","UNARY_OPERATORS","UPDATE_OPERATORS","declarations","withoutInit","constOrLetOrVar","usingOrAwaitUsing","parentIsForX","decl","definite","superClass","implements","mixins","abstract","importAttributes","attributes","assertions","deprecated","source","exportKind","validateOptional","declaration","validateType","specifiers","sourced","sourceless","local","exported","lval","await","module","phase","importKind","options","classMethodOrPropertyCommon","accessibility","static","override","classMethodOrDeclareMethodCommon","access","tag","quasi","assertShape","raw","cooked","templateElementCookedValidator","unterminatedCalled","str","firstInvalidLoc","readStringContents","unterminated","strictNumericEscape","invalidEscapeSequence","numericSeparatorInEscapeSequence","unexpectedNumericSeparator","invalidDigit","invalidCodePoint","tail","quasis","delegate","assertOptionalChainStart","readonly","variance"],"sources":["../../src/definitions/core.ts"],"sourcesContent":["import is from \"../validators/is.ts\";\nimport isValidIdentifier from \"../validators/isValidIdentifier.ts\";\nimport { isKeyword, isReservedWord } from \"@babel/helper-validator-identifier\";\nimport type * as t from \"../index.ts\";\nimport { readStringContents } from \"@babel/helper-string-parser\";\n\nimport {\n  BINARY_OPERATORS,\n  LOGICAL_OPERATORS,\n  ASSIGNMENT_OPERATORS,\n  UNARY_OPERATORS,\n  UPDATE_OPERATORS,\n} from \"../const