.calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  outline: none;
}

/* 日历头部 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: 0 var(--spacing-sm);
}

.month-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.nav-button {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background-color: var(--border-color);
  transform: translateY(-1px);
}

.nav-button:active {
  transform: translateY(0);
}

/* 星期标题 */
.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: var(--spacing-md);
  padding: 0 var(--spacing-sm);
}

.weekday {
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 日历网格 */
.calendar-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 1px;
  background-color: var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  min-height: 400px;
}

.calendar-day {
  background-color: var(--background);
  padding: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 60px;
}

.calendar-day:hover {
  background-color: var(--background-secondary);
}

.calendar-day.other-month {
  background-color: var(--background-secondary);
  color: var(--text-muted);
}

.calendar-day.other-month:hover {
  background-color: var(--border-color);
}

.calendar-day.today {
  background-color: rgba(37, 99, 235, 0.1);
}

.calendar-day.today .day-number {
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.calendar-day.selected {
  background-color: rgba(37, 99, 235, 0.2);
  box-shadow: inset 0 0 0 2px var(--primary-color);
}

.calendar-day.has-tasks {
  background-color: rgba(16, 185, 129, 0.05);
}

.calendar-day.has-tasks.selected {
  background-color: rgba(37, 99, 235, 0.2);
}

.day-number {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.calendar-day.other-month .day-number {
  color: var(--text-muted);
}

/* 任务指示器 */
.task-indicator {
  margin-top: auto;
  display: flex;
  justify-content: center;
}

.task-count {
  background-color: var(--success-color);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-grid {
    min-height: 300px;
  }
  
  .calendar-day {
    min-height: 50px;
    padding: var(--spacing-xs);
  }
  
  .day-number {
    font-size: var(--font-size-xs);
  }
  
  .month-title {
    font-size: var(--font-size-xl);
  }
  
  .nav-button {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-base);
  }
}
