.task-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background-color: var(--background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.task-item:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow);
}

.task-item.completed {
  opacity: 0.7;
  background-color: var(--background-secondary);
}

.task-item.completed .task-title {
  text-decoration: line-through;
  color: var(--text-muted);
}

/* 复选框 */
.task-checkbox {
  flex-shrink: 0;
  padding-top: 2px;
}

.checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--success-color);
}

/* 任务内容 */
.task-content {
  flex: 1;
  min-width: 0;
}

.task-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.task-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
  word-break: break-word;
}

.task-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.action-button {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  opacity: 0.7;
}

.action-button:hover {
  opacity: 1;
  background-color: var(--background-secondary);
  transform: scale(1.1);
}

.action-button.edit:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.action-button.delete:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

/* 任务时间 */
.task-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

/* 任务描述 */
.task-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
  word-break: break-word;
}

/* 任务元信息 */
.task-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.task-priority {
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-reminder {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-item {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .task-header {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .task-actions {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .task-title {
    font-size: var(--font-size-sm);
  }
  
  .task-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
