import { ipc<PERSON><PERSON>, BrowserWindow, Notification } from 'electron'
import { readFileSync, writeFileSync, existsSync } from 'fs'
import { Task, AppSettings } from '@/shared/types'
import { IPC_EVENTS, DEFAULT_SETTINGS } from '@/shared/constants'
import { getTasksFilePath, getSettingsFilePath, log, logError } from './utils'

// 设置IPC通信处理
export function setupIPC(mainWindow: BrowserWindow | null): void {
  // 获取任务数据
  ipcMain.handle(IPC_EVENTS.GET_TASKS, async (): Promise<Task[]> => {
    try {
      const tasksFilePath = getTasksFilePath()
      
      if (!existsSync(tasksFilePath)) {
        log('Tasks file does not exist, returning empty array')
        return []
      }
      
      const data = readFileSync(tasksFilePath, 'utf-8')
      const tasks = JSON.parse(data) as Task[]
      
      log(`Loaded ${tasks.length} tasks from file`)
      return tasks
    } catch (error) {
      logError('Failed to load tasks', error)
      return []
    }
  })

  // 保存任务数据
  ipcMain.handle(IPC_EVENTS.SAVE_TASK, async (event, task: Task): Promise<boolean> => {
    try {
      const tasksFilePath = getTasksFilePath()
      let tasks: Task[] = []
      
      // 读取现有任务
      if (existsSync(tasksFilePath)) {
        const data = readFileSync(tasksFilePath, 'utf-8')
        tasks = JSON.parse(data) as Task[]
      }
      
      // 查找是否存在相同ID的任务
      const existingIndex = tasks.findIndex(t => t.id === task.id)
      
      if (existingIndex >= 0) {
        // 更新现有任务
        tasks[existingIndex] = task
        log(`Updated task: ${task.title}`)
      } else {
        // 添加新任务
        tasks.push(task)
        log(`Added new task: ${task.title}`)
      }
      
      // 保存到文件
      writeFileSync(tasksFilePath, JSON.stringify(tasks, null, 2), 'utf-8')
      
      return true
    } catch (error) {
      logError('Failed to save task', error)
      return false
    }
  })

  // 删除任务
  ipcMain.handle(IPC_EVENTS.DELETE_TASK, async (event, taskId: string): Promise<boolean> => {
    try {
      const tasksFilePath = getTasksFilePath()
      
      if (!existsSync(tasksFilePath)) {
        log('Tasks file does not exist')
        return false
      }
      
      const data = readFileSync(tasksFilePath, 'utf-8')
      let tasks = JSON.parse(data) as Task[]
      
      // 过滤掉要删除的任务
      const originalLength = tasks.length
      tasks = tasks.filter(task => task.id !== taskId)
      
      if (tasks.length === originalLength) {
        log(`Task with ID ${taskId} not found`)
        return false
      }
      
      // 保存更新后的任务列表
      writeFileSync(tasksFilePath, JSON.stringify(tasks, null, 2), 'utf-8')
      
      log(`Deleted task with ID: ${taskId}`)
      return true
    } catch (error) {
      logError('Failed to delete task', error)
      return false
    }
  })

  // 获取应用设置
  ipcMain.handle(IPC_EVENTS.GET_SETTINGS, async (): Promise<AppSettings> => {
    try {
      const settingsFilePath = getSettingsFilePath()
      
      if (!existsSync(settingsFilePath)) {
        log('Settings file does not exist, returning default settings')
        return DEFAULT_SETTINGS
      }
      
      const data = readFileSync(settingsFilePath, 'utf-8')
      const settings = JSON.parse(data) as AppSettings
      
      // 合并默认设置以确保所有字段都存在
      const mergedSettings = { ...DEFAULT_SETTINGS, ...settings }
      
      log('Loaded settings from file')
      return mergedSettings
    } catch (error) {
      logError('Failed to load settings', error)
      return DEFAULT_SETTINGS
    }
  })

  // 保存应用设置
  ipcMain.handle(IPC_EVENTS.SAVE_SETTINGS, async (event, settings: AppSettings): Promise<boolean> => {
    try {
      const settingsFilePath = getSettingsFilePath()
      
      writeFileSync(settingsFilePath, JSON.stringify(settings, null, 2), 'utf-8')
      
      log('Saved settings to file')
      return true
    } catch (error) {
      logError('Failed to save settings', error)
      return false
    }
  })

  // 显示系统通知
  ipcMain.handle(IPC_EVENTS.SHOW_NOTIFICATION, async (event, title: string, body: string): Promise<void> => {
    try {
      if (Notification.isSupported()) {
        const notification = new Notification({
          title,
          body,
          icon: undefined, // 可以添加图标路径
        })
        
        notification.show()
        log(`Showed notification: ${title}`)
      } else {
        log('Notifications are not supported on this system')
      }
    } catch (error) {
      logError('Failed to show notification', error)
    }
  })

  // 最小化到托盘
  ipcMain.handle(IPC_EVENTS.MINIMIZE_TO_TRAY, async (): Promise<void> => {
    try {
      if (mainWindow) {
        mainWindow.hide()
        log('Window minimized to tray')
      }
    } catch (error) {
      logError('Failed to minimize to tray', error)
    }
  })

  // 退出应用
  ipcMain.handle(IPC_EVENTS.QUIT_APP, async (): Promise<void> => {
    try {
      log('Quitting application')
      if (process.platform !== 'darwin') {
        require('electron').app.quit()
      }
    } catch (error) {
      logError('Failed to quit application', error)
    }
  })

  log('IPC handlers setup complete')
}
