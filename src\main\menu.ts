import { Menu, MenuItemConstructorOptions, BrowserWindow, app, shell } from 'electron'
import { isDev } from './utils'

export function createMenu(mainWindow: BrowserWindow | null): Menu {
  const template: MenuItemConstructorOptions[] = [
    // 文件菜单
    {
      label: 'File',
      submenu: [
        {
          label: 'New Task',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-new-task')
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Import Tasks',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-import-tasks')
            }
          }
        },
        {
          label: 'Export Tasks',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-export-tasks')
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },

    // 编辑菜单
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },

    // 视图菜单
    {
      label: 'View',
      submenu: [
        {
          label: 'Today',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-go-to-today')
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Previous Month',
          accelerator: 'CmdOrCtrl+Left',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-prev-month')
            }
          }
        },
        {
          label: 'Next Month',
          accelerator: 'CmdOrCtrl+Right',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-next-month')
            }
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },

    // 窗口菜单
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' as const },
          { role: 'front' as const }
        ] : [])
      ]
    },

    // 帮助菜单
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Task Calendar',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-about')
            }
          }
        },
        {
          label: 'Keyboard Shortcuts',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-shortcuts')
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            shell.openExternal('https://github.com/your-repo/task-calendar/issues')
          }
        },
        {
          label: 'Learn More',
          click: () => {
            shell.openExternal('https://github.com/your-repo/task-calendar')
          }
        }
      ]
    }
  ]

  // macOS特定的菜单调整
  if (process.platform === 'darwin') {
    // 在macOS上，第一个菜单项是应用名称
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        {
          label: 'Preferences',
          accelerator: 'Cmd+,',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send('menu-preferences')
            }
          }
        },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    })

    // 编辑菜单
    const editMenu = template.find(menu => menu.label === 'Edit')
    if (editMenu && editMenu.submenu && Array.isArray(editMenu.submenu)) {
      editMenu.submenu.push(
        { type: 'separator' },
        {
          label: 'Speech',
          submenu: [
            { role: 'startSpeaking' },
            { role: 'stopSpeaking' }
          ]
        }
      )
    }

    // 窗口菜单
    const windowMenu = template.find(menu => menu.label === 'Window')
    if (windowMenu && windowMenu.submenu && Array.isArray(windowMenu.submenu)) {
      windowMenu.submenu = [
        { role: 'close' },
        { role: 'minimize' },
        { role: 'zoom' },
        { type: 'separator' },
        { role: 'front' }
      ]
    }
  }

  // 开发环境添加额外的调试菜单
  if (isDev) {
    const viewMenu = template.find(menu => menu.label === 'View')
    if (viewMenu && viewMenu.submenu && Array.isArray(viewMenu.submenu)) {
      viewMenu.submenu.push(
        { type: 'separator' },
        {
          label: 'Developer',
          submenu: [
            {
              label: 'Reload',
              accelerator: 'CmdOrCtrl+R',
              click: () => {
                if (mainWindow) {
                  mainWindow.reload()
                }
              }
            },
            {
              label: 'Force Reload',
              accelerator: 'CmdOrCtrl+Shift+R',
              click: () => {
                if (mainWindow) {
                  mainWindow.webContents.reloadIgnoringCache()
                }
              }
            },
            {
              label: 'Toggle Developer Tools',
              accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
              click: () => {
                if (mainWindow) {
                  mainWindow.webContents.toggleDevTools()
                }
              }
            }
          ]
        }
      )
    }
  }

  return Menu.buildFromTemplate(template)
}
