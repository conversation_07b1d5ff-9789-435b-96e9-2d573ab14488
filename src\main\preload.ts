import { contextBridge, ipcRenderer } from 'electron'
import { Task, AppSettings } from '@/shared/types'
import { IPC_EVENTS } from '@/shared/constants'

// 定义暴露给渲染进程的API接口
export interface ElectronAPI {
  // 任务相关API
  getTasks: () => Promise<Task[]>
  saveTask: (task: Task) => Promise<boolean>
  deleteTask: (taskId: string) => Promise<boolean>
  
  // 设置相关API
  getSettings: () => Promise<AppSettings>
  saveSettings: (settings: AppSettings) => Promise<boolean>
  
  // 系统相关API
  showNotification: (title: string, body: string) => Promise<void>
  minimizeToTray: () => Promise<void>
  quitApp: () => Promise<void>
  
  // 菜单事件监听
  onMenuAction: (callback: (action: string, data?: any) => void) => void
  removeMenuListeners: () => void
  
  // 平台信息
  platform: string
  isPackaged: boolean
}

// 通过contextBridge安全地暴露API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 任务相关API
  getTasks: (): Promise<Task[]> => {
    return ipcRenderer.invoke(IPC_EVENTS.GET_TASKS)
  },
  
  saveTask: (task: Task): Promise<boolean> => {
    return ipcRenderer.invoke(IPC_EVENTS.SAVE_TASK, task)
  },
  
  deleteTask: (taskId: string): Promise<boolean> => {
    return ipcRenderer.invoke(IPC_EVENTS.DELETE_TASK, taskId)
  },
  
  // 设置相关API
  getSettings: (): Promise<AppSettings> => {
    return ipcRenderer.invoke(IPC_EVENTS.GET_SETTINGS)
  },
  
  saveSettings: (settings: AppSettings): Promise<boolean> => {
    return ipcRenderer.invoke(IPC_EVENTS.SAVE_SETTINGS, settings)
  },
  
  // 系统相关API
  showNotification: (title: string, body: string): Promise<void> => {
    return ipcRenderer.invoke(IPC_EVENTS.SHOW_NOTIFICATION, title, body)
  },
  
  minimizeToTray: (): Promise<void> => {
    return ipcRenderer.invoke(IPC_EVENTS.MINIMIZE_TO_TRAY)
  },
  
  quitApp: (): Promise<void> => {
    return ipcRenderer.invoke(IPC_EVENTS.QUIT_APP)
  },
  
  // 菜单事件监听
  onMenuAction: (callback: (action: string, data?: any) => void): void => {
    // 监听各种菜单事件
    const menuEvents = [
      'menu-new-task',
      'menu-import-tasks',
      'menu-export-tasks',
      'menu-go-to-today',
      'menu-prev-month',
      'menu-next-month',
      'menu-about',
      'menu-shortcuts',
      'menu-preferences'
    ]
    
    menuEvents.forEach(event => {
      ipcRenderer.on(event, (_, data) => {
        callback(event.replace('menu-', ''), data)
      })
    })
  },
  
  removeMenuListeners: (): void => {
    // 移除所有菜单事件监听器
    const menuEvents = [
      'menu-new-task',
      'menu-import-tasks',
      'menu-export-tasks',
      'menu-go-to-today',
      'menu-prev-month',
      'menu-next-month',
      'menu-about',
      'menu-shortcuts',
      'menu-preferences'
    ]
    
    menuEvents.forEach(event => {
      ipcRenderer.removeAllListeners(event)
    })
  },
  
  // 平台信息
  platform: process.platform,
  isPackaged: process.env.NODE_ENV !== 'development'
} as ElectronAPI)

// 类型声明，让TypeScript知道window对象上有electronAPI
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
