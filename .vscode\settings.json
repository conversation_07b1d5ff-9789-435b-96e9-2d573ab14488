{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": ["."], "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/*.log": true}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "typescript.preferences.includePackageJsonAutoImports": "on", "javascript.preferences.includePackageJsonAutoImports": "on"}