#!/usr/bin/env node

/**
 * 开发环境验证脚本
 * 检查所有必要的工具和配置是否正确设置
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 验证开发环境设置...\n')

// 检查文件是否存在
function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath)
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`)
  return exists
}

// 检查目录是否存在
function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()
  console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`)
  return exists
}

// 检查package.json中的依赖
function checkDependency(packageName, type = 'devDependencies') {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const deps = packageJson[type] || {}
    const exists = packageName in deps
    console.log(`${exists ? '✅' : '❌'} ${type} - ${packageName}`)
    return exists
  } catch (error) {
    console.log(`❌ 无法读取 package.json: ${error.message}`)
    return false
  }
}

let allChecksPass = true

console.log('📦 检查项目结构:')
const requiredDirs = [
  ['src', '源代码目录'],
  ['src/main', '主进程目录'],
  ['src/renderer', '渲染进程目录'],
  ['src/shared', '共享代码目录'],
  ['public', '静态资源目录'],
  ['dist', '构建输出目录'],
]

requiredDirs.forEach(([dir, desc]) => {
  if (!checkDirectory(dir, desc)) allChecksPass = false
})

console.log('\n📄 检查配置文件:')
const requiredFiles = [
  ['package.json', 'NPM配置文件'],
  ['tsconfig.json', 'TypeScript配置文件'],
  ['tsconfig.main.json', '主进程TypeScript配置'],
  ['vite.config.ts', 'Vite配置文件'],
  ['.eslintrc.cjs', 'ESLint配置文件'],
  ['.prettierrc', 'Prettier配置文件'],
  ['.gitignore', 'Git忽略文件'],
  ['electron-builder.json', 'Electron Builder配置'],
]

requiredFiles.forEach(([file, desc]) => {
  if (!checkFile(file, desc)) allChecksPass = false
})

console.log('\n🔧 检查开发工具依赖:')
const devDependencies = [
  'typescript',
  'eslint',
  'prettier',
  'electron',
  'vite',
  '@vitejs/plugin-react',
  'concurrently',
  'wait-on',
]

devDependencies.forEach(dep => {
  if (!checkDependency(dep)) allChecksPass = false
})

console.log('\n📚 检查运行时依赖:')
const dependencies = [
  'react',
  'react-dom',
  'zustand',
  'date-fns',
  'electron-store',
]

dependencies.forEach(dep => {
  if (!checkDependency(dep, 'dependencies')) allChecksPass = false
})

console.log('\n🎯 检查核心文件:')
const coreFiles = [
  ['src/renderer/index.tsx', 'React入口文件'],
  ['src/renderer/App.tsx', '主应用组件'],
  ['src/main/main.ts', 'Electron主进程'],
  ['src/main/preload.ts', '预加载脚本'],
  ['src/shared/types/index.ts', '共享类型定义'],
  ['src/shared/constants/index.ts', '共享常量'],
]

coreFiles.forEach(([file, desc]) => {
  if (!checkFile(file, desc)) allChecksPass = false
})

console.log('\n📋 检查VSCode配置:')
const vscodeFiles = [
  ['.vscode/settings.json', 'VSCode设置'],
  ['.vscode/extensions.json', '推荐扩展'],
  ['.vscode/launch.json', '调试配置'],
  ['.vscode/tasks.json', '任务配置'],
]

vscodeFiles.forEach(([file, desc]) => {
  checkFile(file, desc) // 不影响总体结果，只是提示
})

console.log('\n' + '='.repeat(50))
if (allChecksPass) {
  console.log('🎉 所有必要的文件和配置都已正确设置！')
  console.log('💡 你可以运行以下命令开始开发:')
  console.log('   npm run dev')
} else {
  console.log('⚠️  发现一些问题，请检查上面标记为 ❌ 的项目')
  console.log('💡 你可能需要运行: npm install')
}
console.log('='.repeat(50))
