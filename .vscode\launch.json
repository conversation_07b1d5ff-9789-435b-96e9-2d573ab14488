{"version": "0.2.0", "configurations": [{"name": "Debug Electron Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": ["."], "outputCapture": "std", "env": {"NODE_ENV": "development"}, "preLaunchTask": "npm: build:main"}, {"name": "Debug Electron Renderer Process", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/src/renderer", "timeout": 30000}], "compounds": [{"name": "Debug Electron App", "configurations": ["Debug Electron Main Process", "Debug Electron Renderer Process"]}]}