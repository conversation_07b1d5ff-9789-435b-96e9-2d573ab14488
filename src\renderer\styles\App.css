.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background);
}

.app-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background);
  flex-shrink: 0;
}

.app-header h1 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.app-content {
  flex: 1;
  display: flex;
  min-height: 0; /* 允许子元素收缩 */
}

.calendar-section {
  flex: 1;
  padding: var(--spacing-lg);
  border-right: 1px solid var(--border-color);
  overflow: hidden;
}

.tasks-section {
  width: 400px;
  padding: var(--spacing-lg);
  background-color: var(--background-secondary);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .app-content {
    flex-direction: column;
  }
  
  .calendar-section {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    flex: 0 0 auto;
  }
  
  .tasks-section {
    width: 100%;
    flex: 1;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .app-header h1 {
    font-size: var(--font-size-xl);
  }
  
  .calendar-section,
  .tasks-section {
    padding: var(--spacing-md);
  }
}
