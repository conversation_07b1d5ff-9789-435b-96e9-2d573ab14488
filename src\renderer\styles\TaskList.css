.task-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  outline: none;
}

/* 任务列表头部 */
.task-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  flex-shrink: 0;
}

.task-list-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.add-task-button {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}

.add-task-button:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.add-task-button:active {
  transform: translateY(0);
}

.add-task-button:disabled {
  background-color: var(--text-muted);
}

/* 任务项容器 */
.task-items {
  flex: 1;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

.task-items::-webkit-scrollbar {
  width: 6px;
}

/* 无任务状态 */
.no-tasks {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

.no-tasks p {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .task-list-title {
    text-align: center;
    font-size: var(--font-size-base);
  }
  
  .add-task-button {
    width: 100%;
    justify-content: center;
  }
}
