import React, { useState } from 'react'
import { format } from 'date-fns'
import { Task } from '@/shared/types'
import TaskItem from './TaskItem'
import TaskForm from './TaskForm'
import '../styles/TaskList.css'

interface TaskListProps {
  date: Date
  tasks: Task[]
  onTaskAdd: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => void
  onTaskDelete: (taskId: string) => void
}

const TaskList: React.FC<TaskListProps> = ({
  date,
  tasks,
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
}) => {
  const [showForm, setShowForm] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)

  // 按时间排序任务
  const sortedTasks = [...tasks].sort((a, b) => {
    return a.startTime.localeCompare(b.startTime)
  })

  // 处理添加任务
  const handleAddTask = () => {
    setEditingTask(null)
    setShowForm(true)
  }

  // 处理编辑任务
  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setShowForm(true)
  }

  // 处理表单提交
  const handleFormSubmit = (taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingTask) {
      onTaskUpdate(editingTask.id, taskData)
    } else {
      onTaskAdd(taskData)
    }
    setShowForm(false)
    setEditingTask(null)
  }

  // 处理表单取消
  const handleFormCancel = () => {
    setShowForm(false)
    setEditingTask(null)
  }

  // 处理任务完成状态切换
  const handleTaskToggle = (taskId: string, completed: boolean) => {
    onTaskUpdate(taskId, { completed })
  }

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (showForm) {
        handleFormCancel()
      }
      event.preventDefault()
    }
  }

  return (
    <div className="task-list" onKeyDown={handleKeyDown} tabIndex={0}>
      {/* 任务列表标题 */}
      <div className="task-list-header">
        <h3 className="task-list-title">
          Tasks {format(date, 'MMMM d, yyyy')}
        </h3>
        <button 
          className="add-task-button"
          onClick={handleAddTask}
          disabled={showForm}
        >
          + Add Task
        </button>
      </div>

      {/* 任务表单 */}
      {showForm && (
        <TaskForm
          task={editingTask}
          date={date}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
        />
      )}

      {/* 任务列表 */}
      <div className="task-items">
        {sortedTasks.length === 0 ? (
          <div className="no-tasks">
            <p>No tasks for this day</p>
          </div>
        ) : (
          sortedTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onEdit={() => handleEditTask(task)}
              onDelete={() => onTaskDelete(task.id)}
              onToggle={(completed) => handleTaskToggle(task.id, completed)}
            />
          ))
        )}
      </div>
    </div>
  )
}

export default TaskList
