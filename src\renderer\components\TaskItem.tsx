import React from 'react'
import { format } from 'date-fns'
import { Task } from '@/shared/types'
import '../styles/TaskItem.css'

interface TaskItemProps {
  task: Task
  onEdit: () => void
  onDelete: () => void
  onToggle: (completed: boolean) => void
}

const TaskItem: React.FC<TaskItemProps> = ({
  task,
  onEdit,
  onDelete,
  onToggle,
}) => {
  // 格式化时间显示
  const formatTime = (timeStr: string) => {
    try {
      const date = new Date(timeStr)
      return format(date, 'h:mm a')
    } catch {
      return timeStr
    }
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#ef4444'
      case 'medium':
        return '#f59e0b'
      case 'low':
        return '#6b7280'
      default:
        return '#6b7280'
    }
  }

  return (
    <div className={`task-item ${task.completed ? 'completed' : ''}`}>
      {/* 任务完成状态 */}
      <div className="task-checkbox">
        <input
          type="checkbox"
          checked={task.completed}
          onChange={(e) => onToggle(e.target.checked)}
          className="checkbox"
        />
      </div>

      {/* 任务内容 */}
      <div className="task-content">
        <div className="task-header">
          <h4 className="task-title">{task.title}</h4>
          <div className="task-actions">
            <button
              className="action-button edit"
              onClick={onEdit}
              title="Edit task"
            >
              ✏️
            </button>
            <button
              className="action-button delete"
              onClick={onDelete}
              title="Delete task"
            >
              🗑️
            </button>
          </div>
        </div>

        <div className="task-time">
          {formatTime(task.startTime)} - {formatTime(task.endTime)}
        </div>

        {task.description && (
          <div className="task-description">
            {task.description}
          </div>
        )}

        <div className="task-meta">
          <span 
            className="task-priority"
            style={{ color: getPriorityColor(task.priority) }}
          >
            {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
          </span>
          
          {task.reminder?.enabled && (
            <span className="task-reminder">
              🔔 {task.reminder.minutesBefore}min before
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default TaskItem
