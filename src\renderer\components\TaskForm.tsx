import React, { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { Task } from '@/shared/types'
import '../styles/TaskForm.css'

interface TaskFormProps {
  task?: Task | null
  date: Date
  onSubmit: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void
  onCancel: () => void
}

const TaskForm: React.FC<TaskFormProps> = ({
  task,
  date,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    priority: 'medium' as const,
    reminderEnabled: false,
    reminderMinutes: 15,
  })

  // 初始化表单数据
  useEffect(() => {
    if (task) {
      setFormData({
        title: task.title,
        description: task.description || '',
        startTime: task.startTime.split('T')[1]?.substring(0, 5) || '',
        endTime: task.endTime.split('T')[1]?.substring(0, 5) || '',
        priority: task.priority,
        reminderEnabled: task.reminder?.enabled || false,
        reminderMinutes: task.reminder?.minutesBefore || 15,
      })
    } else {
      // 新任务默认时间
      const now = new Date()
      const startTime = format(now, 'HH:mm')
      const endTime = format(new Date(now.getTime() + 60 * 60 * 1000), 'HH:mm')
      
      setFormData(prev => ({
        ...prev,
        startTime,
        endTime,
      }))
    }
  }, [task])

  // 处理表单输入
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      alert('Please enter a task title')
      return
    }

    if (!formData.startTime || !formData.endTime) {
      alert('Please enter start and end times')
      return
    }

    // 构建完整的日期时间字符串
    const dateStr = format(date, 'yyyy-MM-dd')
    const startDateTime = `${dateStr}T${formData.startTime}:00`
    const endDateTime = `${dateStr}T${formData.endTime}:00`

    // 验证时间逻辑
    if (startDateTime >= endDateTime) {
      alert('End time must be after start time')
      return
    }

    const taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'> = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      startTime: startDateTime,
      endTime: endDateTime,
      date: dateStr,
      completed: task?.completed || false,
      priority: formData.priority,
      reminder: formData.reminderEnabled ? {
        enabled: true,
        minutesBefore: formData.reminderMinutes,
      } : undefined,
    }

    onSubmit(taskData)
  }

  return (
    <div className="task-form-overlay">
      <form className="task-form" onSubmit={handleSubmit}>
        <div className="form-header">
          <h3>{task ? 'Edit Task' : 'Add New Task'}</h3>
          <button
            type="button"
            className="close-button"
            onClick={onCancel}
          >
            ×
          </button>
        </div>

        <div className="form-body">
          {/* 任务标题 */}
          <div className="form-group">
            <label htmlFor="title">Task Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter task title"
              maxLength={100}
              required
            />
          </div>

          {/* 时间设置 */}
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="startTime">Start Time *</label>
              <input
                type="time"
                id="startTime"
                name="startTime"
                value={formData.startTime}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="endTime">End Time *</label>
              <input
                type="time"
                id="endTime"
                name="endTime"
                value={formData.endTime}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          {/* 优先级 */}
          <div className="form-group">
            <label htmlFor="priority">Priority</label>
            <select
              id="priority"
              name="priority"
              value={formData.priority}
              onChange={handleInputChange}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          {/* 描述 */}
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter task description (optional)"
              maxLength={500}
              rows={3}
            />
          </div>

          {/* 提醒设置 */}
          <div className="form-group">
            <div className="checkbox-group">
              <input
                type="checkbox"
                id="reminderEnabled"
                name="reminderEnabled"
                checked={formData.reminderEnabled}
                onChange={handleInputChange}
              />
              <label htmlFor="reminderEnabled">Enable reminder</label>
            </div>
            
            {formData.reminderEnabled && (
              <div className="reminder-options">
                <label htmlFor="reminderMinutes">Remind me</label>
                <select
                  id="reminderMinutes"
                  name="reminderMinutes"
                  value={formData.reminderMinutes}
                  onChange={handleInputChange}
                >
                  <option value={5}>5 minutes before</option>
                  <option value={15}>15 minutes before</option>
                  <option value={30}>30 minutes before</option>
                  <option value={60}>1 hour before</option>
                </select>
              </div>
            )}
          </div>
        </div>

        <div className="form-footer">
          <button type="button" className="cancel-button" onClick={onCancel}>
            Cancel
          </button>
          <button type="submit" className="submit-button">
            {task ? 'Update Task' : 'Add Task'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default TaskForm
