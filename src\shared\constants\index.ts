// 应用常量
export const APP_NAME = 'Task Calendar';
export const APP_VERSION = '1.0.0';

// 文件路径常量
export const DATA_DIR = 'TaskCalendar';
export const TASKS_FILE = 'tasks.json';
export const SETTINGS_FILE = 'settings.json';
export const BACKUP_DIR = 'backups';

// 默认设置
export const DEFAULT_SETTINGS = {
  theme: 'light' as const,
  fontSize: 'medium' as const,
  timeFormat: '24h' as const,
  weekStartsOn: 1 as const, // 周一开始
  autoStartup: false,
  reminderSound: true,
  windowBounds: {
    width: 1200,
    height: 800,
  },
};

// 时间格式
export const DATE_FORMAT = 'yyyy-MM-dd';
export const TIME_FORMAT = 'HH:mm';
export const DATETIME_FORMAT = 'yyyy-MM-dd HH:mm';

// 提醒时间选项（分钟）
export const REMINDER_OPTIONS = [
  { label: '5 minutes before', value: 5 },
  { label: '15 minutes before', value: 15 },
  { label: '30 minutes before', value: 30 },
  { label: '1 hour before', value: 60 },
];

// 优先级选项
export const PRIORITY_OPTIONS = [
  { label: 'High', value: 'high' as const, color: '#ef4444' },
  { label: 'Medium', value: 'medium' as const, color: '#f59e0b' },
  { label: 'Low', value: 'low' as const, color: '#6b7280' },
];

// 重复类型选项
export const REPEAT_OPTIONS = [
  { label: 'No repeat', value: 'none' },
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' },
];

// IPC 事件名称
export const IPC_EVENTS = {
  // 任务相关
  GET_TASKS: 'get-tasks',
  SAVE_TASK: 'save-task',
  DELETE_TASK: 'delete-task',
  UPDATE_TASK: 'update-task',
  
  // 设置相关
  GET_SETTINGS: 'get-settings',
  SAVE_SETTINGS: 'save-settings',
  
  // 系统相关
  SHOW_NOTIFICATION: 'show-notification',
  MINIMIZE_TO_TRAY: 'minimize-to-tray',
  QUIT_APP: 'quit-app',
} as const;
